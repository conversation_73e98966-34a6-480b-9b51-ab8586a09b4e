import { Suspense } from "react";
import Navigation from "@/components/Navigation";
import DatabasePageContent from "./DatabasePageContent";
import SimpleAccessCheck from "@/components/SimpleAccessCheck";

interface DatabasePageProps {
  params: Promise<{
    database: string;
  }>;
}

export default async function DatabasePage({ params }: DatabasePageProps) {
  const resolvedParams = await params;

  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation showSearch />
      <SimpleAccessCheck database={resolvedParams.database}>
        <Suspense fallback={
          <div className="flex justify-center items-center h-64">
            <div className="text-gray-500">加载中...</div>
          </div>
        }>
          <DatabasePageContent database={resolvedParams.database} />
        </Suspense>
      </SimpleAccessCheck>
    </div>
  );
}
